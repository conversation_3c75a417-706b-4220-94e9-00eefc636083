{"rustc": 10895048813736897673, "features": "[\"default\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 15657897354478470176, "path": 13607162284785985193, "deps": [[3722963349756955755, "once_cell", false, 13806597229932172998], [6166349630582887940, "bitflags", false, 4136670486078863305], [6635237767502169825, "foreign_types", false, 6417661196054707440], [8194304432723500424, "libc", false, 13797033458145130602], [10099563100786658307, "openssl_macros", false, 6279837481749931543], [10411997081178400487, "cfg_if", false, 17268683266899850563], [12853863448654256444, "build_script_build", false, 240866318518444856], [16114643047409176557, "ffi", false, 1636479781950037904]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\openssl-113f78db2a983ae7\\dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}