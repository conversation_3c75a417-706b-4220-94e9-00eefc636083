{"rustc": 10895048813736897673, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5788819337146887687, "path": 4443157875455849821, "deps": [[3722963349756955755, "once_cell", false, 12825410154089437232], [5491919304041016563, "ring", false, 2848889501881926705], [5986029879202738730, "log", false, 5644347006075319131], [6528079939221783635, "zeroize", false, 3434478208448496389], [8880649100394045925, "build_script_build", false, 3130630291490197103], [11782200238436109817, "<PERSON><PERSON><PERSON>", false, 4713491955363141054], [16009134724182327169, "pki_types", false, 6780114180160311395], [17003143334332120809, "subtle", false, 16610867709910943612]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-c202045b2ef7cde2\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}