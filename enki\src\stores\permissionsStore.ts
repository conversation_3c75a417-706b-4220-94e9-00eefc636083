import { defineStore } from "pinia";
import { ref } from "vue";
import type { 
    PermissionCheckRequest, 
    PermissionCheckResponse, 
    PermissionCheckResult,
    BulkRelationshipOperation 
} from "@/types";
import { checkPermissions, bulkRelationshipOperations } from "@/services/kiApi";
import { useSpacesStore } from "./spacesStore";

export const usePermissionsStore = defineStore('permissions', {
    state: () => ({
        // Cache permission check results for performance
        permissionCache: ref<Record<string, PermissionCheckResult>>({}),
        loading: ref(false),
        error: ref<string | null>(null)
    }),
    
    getters: {
        // Get cached permission result
        getCachedPermission: (state) => (
            resourceType: string, 
            resourceId: string, 
            permission: string, 
            subjectType: string, 
            subjectId: string
        ) => {
            const key = `${resourceType}:${resourceId}:${permission}:${subjectType}:${subjectId}`;
            return state.permissionCache[key];
        },
        
        // Check if user has permission (from cache)
        hasPermission: (state) => (
            resourceType: string, 
            resourceId: string, 
            permission: string, 
            subjectType: string, 
            subjectId: string
        ) => {
            const cached = state.permissionCache[`${resourceType}:${resourceId}:${permission}:${subjectType}:${subjectId}`];
            return cached?.allowed || false;
        }
    },
    
    actions: {
        async checkPermissions(request: PermissionCheckRequest): Promise<PermissionCheckResponse | null> {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return null;
            }
            
            this.loading = true;
            this.error = null;
            
            try {
                const response = await checkPermissions(server, request);
                
                // Cache the results
                response.results.forEach(result => {
                    const key = `${result.resource_type}:${result.resource_id}:${result.permission}:${result.subject_type}:${result.subject_id}`;
                    this.permissionCache[key] = result;
                });
                
                return response;
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to check permissions:', error);
                return null;
            } finally {
                this.loading = false;
            }
        },
        
        async checkSinglePermission(
            resourceType: string, 
            resourceId: string, 
            permission: string, 
            subjectType: string, 
            subjectId: string
        ): Promise<boolean> {
            // Check cache first
            const cached = this.getCachedPermission(resourceType, resourceId, permission, subjectType, subjectId);
            if (cached !== undefined) {
                return cached.allowed;
            }
            
            // Make API call
            const response = await this.checkPermissions({
                checks: [{
                    resource_type: resourceType,
                    resource_id: resourceId,
                    permission: permission,
                    subject_type: subjectType,
                    subject_id: subjectId
                }]
            });
            
            return response?.results[0]?.allowed || false;
        },
        
        async bulkRelationshipOperations(operations: BulkRelationshipOperation[]): Promise<boolean> {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return false;
            }
            
            this.loading = true;
            this.error = null;
            
            try {
                await bulkRelationshipOperations(server, operations);
                // Clear cache as relationships have changed
                this.clearCache();
                return true;
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to perform bulk operations:', error);
                return false;
            } finally {
                this.loading = false;
            }
        },
        
        // Helper methods for common operations
        async addRoleMember(roleId: string, userId: string): Promise<boolean> {
            return await this.bulkRelationshipOperations([{
                operation: 'create',
                relationships: [{
                    resource_type: 'role',
                    resource_id: roleId,
                    relation: 'member',
                    subject_type: 'user',
                    subject_id: userId
                }]
            }]);
        },
        
        async removeRoleMember(roleId: string, userId: string): Promise<boolean> {
            return await this.bulkRelationshipOperations([{
                operation: 'delete',
                relationships: [{
                    resource_type: 'role',
                    resource_id: roleId,
                    relation: 'member',
                    subject_type: 'user',
                    subject_id: userId
                }]
            }]);
        },
        
        async setTaskPermission(
            taskId: string, 
            permission: 'owner' | 'viewer' | 'editor' | 'admin',
            subjectType: 'user' | 'role',
            subjectId: string
        ): Promise<boolean> {
            const relation = subjectType === 'user' ? `${permission}_user` : `${permission}_role`;
            
            return await this.bulkRelationshipOperations([{
                operation: 'create',
                relationships: [{
                    resource_type: 'task',
                    resource_id: taskId,
                    relation: relation,
                    subject_type: subjectType,
                    subject_id: subjectId
                }]
            }]);
        },
        
        async removeTaskPermission(
            taskId: string, 
            permission: 'owner' | 'viewer' | 'editor' | 'admin',
            subjectType: 'user' | 'role',
            subjectId: string
        ): Promise<boolean> {
            const relation = subjectType === 'user' ? `${permission}_user` : `${permission}_role`;
            
            return await this.bulkRelationshipOperations([{
                operation: 'delete',
                relationships: [{
                    resource_type: 'task',
                    resource_id: taskId,
                    relation: relation,
                    subject_type: subjectType,
                    subject_id: subjectId
                }]
            }]);
        },
        
        clearCache() {
            this.permissionCache = {};
        },
        
        clearError() {
            this.error = null;
        }
    }
});
