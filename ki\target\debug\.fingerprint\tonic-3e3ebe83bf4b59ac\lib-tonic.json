{"rustc": 10895048813736897673, "features": "[\"channel\", \"codegen\", \"default\", \"prost\", \"router\", \"server\", \"tls\", \"transport\"]", "declared_features": "[\"channel\", \"codegen\", \"default\", \"gzip\", \"prost\", \"router\", \"server\", \"tls\", \"tls-native-roots\", \"tls-roots\", \"tls-webpki-roots\", \"transport\", \"zstd\"]", "target": 7201140320892410334, "profile": 15657897354478470176, "path": 4948536373788525058, "deps": [[40386456601120721, "percent_encoding", false, 7865310525752909446], [784494742817713399, "tower_service", false, 4681011003402000622], [1188017320647144970, "async_stream", false, 13684507483798381891], [2788412271010997089, "hyper_timeout", false, 12685667527322307454], [3601586811267292532, "tower", false, 664595068116493310], [4891297352905791595, "axum", false, 15514216657992687228], [5138218615291878843, "tokio", false, 2061717018272964870], [6264115378959545688, "pin_project", false, 9411144807091723316], [7712452662827335977, "tower_layer", false, 4286392900353169829], [8431740714262224655, "socket2", false, 15271450076252967050], [8606274917505247608, "tracing", false, 1060383571196582040], [9010263965687315507, "http", false, 8244477843949077449], [9298649433536336071, "prost", false, 6272899646120993406], [10595802073777078462, "hyper_util", false, 2640929072661463055], [11895591994124935963, "tokio_rustls", false, 4843472259115546582], [11946729385090170470, "async_trait", false, 10765758345576346120], [11957360342995674422, "hyper", false, 9535073700102981167], [13077212702700853852, "base64", false, 16688224880384710644], [13868379202103418305, "h2", false, 8016773368217710837], [14084095096285906100, "http_body", false, 2259226771228292901], [15032952994102373905, "rustls_pemfile", false, 2317040812707255610], [16066129441945555748, "bytes", false, 5504921018255351814], [16900715236047033623, "http_body_util", false, 12374190028022742957], [16973251432615581304, "tokio_stream", false, 16273957381072675363]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tonic-3e3ebe83bf4b59ac\\dep-lib-tonic", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}