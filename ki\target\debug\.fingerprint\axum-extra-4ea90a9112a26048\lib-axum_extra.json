{"rustc": 10895048813736897673, "features": "[\"cookie\", \"default\", \"tracing\"]", "declared_features": "[\"__private_docs\", \"async-read-body\", \"async-stream\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"error-response\", \"file-stream\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"scheme\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 5085313994138363887, "path": 4569437634808999596, "deps": [[784494742817713399, "tower_service", false, 4681011003402000622], [1906322745568073236, "pin_project_lite", false, 8745154965143889200], [1999399154011168049, "rustversion", false, 12095854159049431513], [5695049318159433696, "tower", false, 7581848508668914960], [7712452662827335977, "tower_layer", false, 4286392900353169829], [9010263965687315507, "http", false, 8244477843949077449], [9689903380558560274, "serde", false, 7510950832928885797], [10229185211513642314, "mime", false, 7251293154870877873], [10629569228670356391, "futures_util", false, 18269551313004061405], [14084095096285906100, "http_body", false, 2259226771228292901], [15176407853393882315, "axum_core", false, 4830615799653554867], [16066129441945555748, "bytes", false, 5504921018255351814], [16727543399706004146, "cookie", false, 4868604412369644732], [16900715236047033623, "http_body_util", false, 12374190028022742957], [17809372758784730012, "axum", false, 6164864148999041643]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-extra-4ea90a9112a26048\\dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}