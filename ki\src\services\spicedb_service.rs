use anyhow::{anyhow, Result};
use std::sync::Arc;
use tokio::sync::RwLock;
use tonic::{
    transport::{Channel, ClientTlsConfig, Certificate, Identity},
    Request,
};
use spicedb_grpc::authzed::api::v1::{
    permissions_service_client::PermissionsServiceClient,
    schema_service_client::SchemaServiceClient,
    CheckPermissionRequest, WriteRelationshipsRequest,
    ObjectReference, SubjectReference, Relationship,
    RelationshipUpdate, ZedToken as GrpcZedToken,
    relationship_update::Operation as RelationshipOperation,
    check_permission_response::Permissionship,
    Consistency, consistency::Requirement,
    WriteSchemaRequest,
};

use crate::db::models::{
    BulkRelationshipOperation, PermissionCheck, PermissionCheckResult,
    RoleMember, RoleTreeNode,
};

/// ZedToken for read-after-write consistency
#[derive(Debug, Clone)]
pub struct ZedToken(pub String);

/// SpiceDB service for managing authorization relationships
#[derive(Clone)]
pub struct SpiceDBService {
    permissions_client: PermissionsServiceClient<Channel>,
    schema_client: SchemaServiceClient<Channel>,
    /// Latest ZedToken for read-after-write consistency
    latest_token: Arc<RwLock<Option<ZedToken>>>,
    /// Pre-shared key for authentication
    preshared_key: String,
}

impl SpiceDBService {
    /// Create a new SpiceDB service with mTLS connection
    pub async fn new(endpoint: &str, cert_path: &str, key_path: &str, ca_path: &str, preshared_key: &str) -> Result<Self> {
        // Read certificate files
        let cert = tokio::fs::read(cert_path).await
            .map_err(|e| anyhow!("Failed to read certificate file {}: {}", cert_path, e))?;
        let key = tokio::fs::read(key_path).await
            .map_err(|e| anyhow!("Failed to read key file {}: {}", key_path, e))?;
        let ca = tokio::fs::read(ca_path).await
            .map_err(|e| anyhow!("Failed to read CA file {}: {}", ca_path, e))?;

        // Configure mTLS
        let tls_config = ClientTlsConfig::new()
            .ca_certificate(Certificate::from_pem(ca))
            .identity(Identity::from_pem(cert, key));

        // Create channel with mTLS
        let channel = Channel::from_shared(endpoint.to_string())?
            .tls_config(tls_config)?
            .connect()
            .await?;

        let permissions_client = PermissionsServiceClient::new(channel.clone());
        let schema_client = SchemaServiceClient::new(channel);

        Ok(Self {
            permissions_client,
            schema_client,
            latest_token: Arc::new(RwLock::new(None)),
            preshared_key: preshared_key.to_string(),
        })
    }

    /// Create a new SpiceDB service for development (no mTLS)
    pub async fn new_insecure(endpoint: &str, preshared_key: &str) -> Result<Self> {
        let channel = Channel::from_shared(endpoint.to_string())?
            .connect()
            .await?;

        let permissions_client = PermissionsServiceClient::new(channel.clone());
        let schema_client = SchemaServiceClient::new(channel);

        Ok(Self {
            permissions_client,
            schema_client,
            latest_token: Arc::new(RwLock::new(None)),
            preshared_key: preshared_key.to_string(),
        })
    }

    /// Create an authenticated request with pre-shared key
    fn create_authenticated_request<T>(&self, inner: T) -> Request<T> {
        let mut request = Request::new(inner);
        let auth_value = format!("Bearer {}", self.preshared_key);
        if let Ok(metadata_value) = auth_value.parse() {
            request.metadata_mut().insert("authorization", metadata_value);
        }
        request
    }

    /// Update the latest ZedToken
    async fn update_token(&self, token: Option<String>) {
        if let Some(token_str) = token {
            let mut latest = self.latest_token.write().await;
            *latest = Some(ZedToken(token_str));
        }
    }

    /// Get the latest ZedToken for consistency
    async fn get_token(&self) -> Option<ZedToken> {
        let latest = self.latest_token.read().await;
        latest.clone()
    }

    /// Create consistency configuration with latest token
    fn create_consistency(&self, token: Option<ZedToken>) -> Option<Consistency> {
        token.map(|t| Consistency {
            requirement: Some(Requirement::AtLeastAsFresh(GrpcZedToken { token: t.0 }))
        })
    }

    /// Create a role membership relationship
    pub async fn add_role_member(&self, role_id: &str, user_id: &str) -> Result<()> {
        let relationship = Relationship {
            resource: Some(ObjectReference {
                object_type: "role".to_string(),
                object_id: role_id.to_string(),
            }),
            relation: "member".to_string(),
            subject: Some(SubjectReference {
                object: Some(ObjectReference {
                    object_type: "user".to_string(),
                    object_id: user_id.to_string(),
                }),
                optional_relation: "".to_string(),
            }),
            optional_caveat: None,
        };

        let update = RelationshipUpdate {
            operation: RelationshipOperation::Create as i32,
            relationship: Some(relationship),
        };

        let request_inner = WriteRelationshipsRequest {
            updates: vec![update],
            optional_preconditions: vec![],
        };

        let request = self.create_authenticated_request(request_inner);
        let mut client = self.permissions_client.clone();

        let response = client.write_relationships(request).await?;
        let inner = response.into_inner();

        // Update token from response
        self.update_token(inner.written_at.map(|t| t.token)).await;

        tracing::info!("Added role member: role={}, user={}", role_id, user_id);
        Ok(())
    }

    /// Remove a role membership relationship
    pub async fn remove_role_member(&self, role_id: &str, user_id: &str) -> Result<()> {
        let relationship = Relationship {
            resource: Some(ObjectReference {
                object_type: "role".to_string(),
                object_id: role_id.to_string(),
            }),
            relation: "member".to_string(),
            subject: Some(SubjectReference {
                object: Some(ObjectReference {
                    object_type: "user".to_string(),
                    object_id: user_id.to_string(),
                }),
                optional_relation: "".to_string(),
            }),
            optional_caveat: None,
        };

        let update = RelationshipUpdate {
            operation: RelationshipOperation::Delete as i32,
            relationship: Some(relationship),
        };

        let request_inner = WriteRelationshipsRequest {
            updates: vec![update],
            optional_preconditions: vec![],
        };

        let request = self.create_authenticated_request(request_inner);
        let mut client = self.permissions_client.clone();

        let response = client.write_relationships(request).await?;
        let inner = response.into_inner();

        // Update token from response
        self.update_token(inner.written_at.map(|t| t.token)).await;

        tracing::info!("Removed role member: role={}, user={}", role_id, user_id);
        Ok(())
    }

    /// Set role parent with atomic compare-and-set operation
    pub async fn set_role_parent(&self, role_id: &str, parent_id: Option<&str>) -> Result<()> {
        let mut updates = vec![];
        let preconditions = vec![];

        if let Some(parent) = parent_id {
            // Create the parent relationship
            let relationship = Relationship {
                resource: Some(ObjectReference {
                    object_type: "role".to_string(),
                    object_id: role_id.to_string(),
                }),
                relation: "parent".to_string(),
                subject: Some(SubjectReference {
                    object: Some(ObjectReference {
                        object_type: "role".to_string(),
                        object_id: parent.to_string(),
                    }),
                    optional_relation: "".to_string(),
                }),
                optional_caveat: None,
            };

            let update = RelationshipUpdate {
                operation: RelationshipOperation::Create as i32,
                relationship: Some(relationship),
            };
            updates.push(update);
        } else {
            // Remove any existing parent relationship
            // Note: This is a simplified approach - in production you might want to
            // first read existing relationships and then delete specific ones
            let relationship = Relationship {
                resource: Some(ObjectReference {
                    object_type: "role".to_string(),
                    object_id: role_id.to_string(),
                }),
                relation: "parent".to_string(),
                subject: None, // This will match any subject for deletion
                optional_caveat: None,
            };

            let update = RelationshipUpdate {
                operation: RelationshipOperation::Delete as i32,
                relationship: Some(relationship),
            };
            updates.push(update);
        }

        let request_inner = WriteRelationshipsRequest {
            updates,
            optional_preconditions: preconditions,
        };

        let request = self.create_authenticated_request(request_inner);
        let mut client = self.permissions_client.clone();

        let response = client.write_relationships(request).await?;
        let inner = response.into_inner();

        // Update token from response
        self.update_token(inner.written_at.map(|t| t.token)).await;

        tracing::info!("Set role parent: role={}, parent={:?}", role_id, parent_id);
        Ok(())
    }

    /// Check if a user has a specific permission
    pub async fn check_permission(
        &self,
        resource_type: &str,
        resource_id: &str,
        permission: &str,
        user_id: &str,
    ) -> Result<bool> {
        let token = self.get_token().await;
        let consistency = self.create_consistency(token);

        let request_inner = CheckPermissionRequest {
            resource: Some(ObjectReference {
                object_type: resource_type.to_string(),
                object_id: resource_id.to_string(),
            }),
            permission: permission.to_string(),
            subject: Some(SubjectReference {
                object: Some(ObjectReference {
                    object_type: "user".to_string(),
                    object_id: user_id.to_string(),
                }),
                optional_relation: "".to_string(),
            }),
            context: None,
            consistency,
            with_tracing: false,
        };

        let request = self.create_authenticated_request(request_inner);
        let mut client = self.permissions_client.clone();

        let response = client.check_permission(request).await?;
        let inner = response.into_inner();

        // Update token from response
        self.update_token(inner.checked_at.map(|t| t.token)).await;

        let allowed = inner.permissionship == Permissionship::HasPermission as i32;

        tracing::debug!(
            "Permission check: resource={}:{}, permission={}, user={}, allowed={}",
            resource_type, resource_id, permission, user_id, allowed
        );

        Ok(allowed)
    }

    /// Batch check multiple permissions
    pub async fn batch_check_permissions(&self, checks: Vec<PermissionCheck>) -> Result<Vec<PermissionCheckResult>> {
        let mut results = Vec::new();

        // For now, we'll do individual checks. In the future, this could be optimized
        // with SpiceDB's batch checking capabilities when they become available
        for check in checks {
            let allowed = self.check_permission(
                &check.resource_type,
                &check.resource_id,
                &check.permission,
                &check.subject_id,
            ).await.unwrap_or(false); // Default to deny on error

            results.push(PermissionCheckResult {
                resource_type: check.resource_type,
                resource_id: check.resource_id,
                permission: check.permission,
                subject_type: check.subject_type,
                subject_id: check.subject_id,
                allowed,
            });
        }

        tracing::debug!("Batch permission check completed: {} checks", results.len());
        Ok(results)
    }

    /// Execute bulk relationship operations
    pub async fn bulk_write_relationships(&self, operations: Vec<BulkRelationshipOperation>) -> Result<()> {
        let mut all_updates = Vec::new();

        for operation in operations {
            for tuple in operation.relationships {
                let relationship = Relationship {
                    resource: Some(ObjectReference {
                        object_type: tuple.resource_type,
                        object_id: tuple.resource_id,
                    }),
                    relation: tuple.relation,
                    subject: Some(SubjectReference {
                        object: Some(ObjectReference {
                            object_type: tuple.subject_type,
                            object_id: tuple.subject_id,
                        }),
                        optional_relation: "".to_string(),
                    }),
                    optional_caveat: None,
                };

                let grpc_operation = match operation.operation {
                    crate::db::models::RelationshipOperation::Create => RelationshipOperation::Create,
                    crate::db::models::RelationshipOperation::Delete => RelationshipOperation::Delete,
                };

                let update = RelationshipUpdate {
                    operation: grpc_operation as i32,
                    relationship: Some(relationship),
                };
                all_updates.push(update);
            }
        }

        if !all_updates.is_empty() {
            let request_inner = WriteRelationshipsRequest {
                updates: all_updates,
                optional_preconditions: vec![],
            };

            let request = self.create_authenticated_request(request_inner);
            let mut client = self.permissions_client.clone();

            let response = client.write_relationships(request).await?;
            let inner = response.into_inner();

            // Update token from response
            self.update_token(inner.written_at.map(|t| t.token)).await;
        }

        tracing::info!("Bulk relationship operations completed");
        Ok(())
    }

    /// Get role members (placeholder - requires lookup subjects implementation)
    pub async fn get_role_members(&self, _role_id: &str) -> Result<Vec<RoleMember>> {
        // TODO: Implement using LookupSubjects API when needed
        tracing::info!("SpiceDB get_role_members called - placeholder implementation");
        Ok(vec![])
    }

    /// Get role tree (placeholder - requires reading relationships)
    pub async fn get_role_tree(&self) -> Result<Vec<RoleTreeNode>> {
        // TODO: Implement using ReadRelationships API to build hierarchy
        tracing::info!("SpiceDB get_role_tree called - placeholder implementation");
        Ok(vec![])
    }

    /// Write schema to SpiceDB
    pub async fn write_schema(&self, schema: &str) -> Result<()> {
        let request_inner = WriteSchemaRequest {
            schema: schema.to_string(),
        };

        let request = self.create_authenticated_request(request_inner);
        let mut client = self.schema_client.clone();

        let response = client.write_schema(request).await?;
        let inner = response.into_inner();

        // Update token from response
        self.update_token(inner.written_at.map(|t| t.token)).await;

        tracing::info!("Schema written to SpiceDB successfully");
        Ok(())
    }
}
