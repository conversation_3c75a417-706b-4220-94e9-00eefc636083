{"rustc": 10895048813736897673, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 9795262057244925075, "deps": [[40386456601120721, "percent_encoding", false, 7865310525752909446], [95042085696191081, "ipnet", false, 13753410297010176641], [784494742817713399, "tower_service", false, 4681011003402000622], [1906322745568073236, "pin_project_lite", false, 8745154965143889200], [2517136641825875337, "sync_wrapper", false, 7530985440802629266], [3150220818285335163, "url", false, 17089163340523727768], [3722963349756955755, "once_cell", false, 13806597229932172998], [5138218615291878843, "tokio", false, 2061717018272964870], [5695049318159433696, "tower", false, 13113902740368234319], [5986029879202738730, "log", false, 16590874038251987204], [7620660491849607393, "futures_core", false, 12714968455384343301], [9010263965687315507, "http", false, 8244477843949077449], [9689903380558560274, "serde", false, 5420700675791837695], [10229185211513642314, "mime", false, 7251293154870877873], [10595802073777078462, "hyper_util", false, 2640929072661463055], [10629569228670356391, "futures_util", false, 18269551313004061405], [11957360342995674422, "hyper", false, 9535073700102981167], [12186126227181294540, "tokio_native_tls", false, 12420062495233530657], [13077212702700853852, "base64", false, 16688224880384710644], [13868379202103418305, "h2", false, 8016773368217710837], [14084095096285906100, "http_body", false, 2259226771228292901], [14564311161534545801, "encoding_rs", false, 2424137802430912579], [15032952994102373905, "rustls_pemfile", false, 2317040812707255610], [15367738274754116744, "serde_json", false, 16065851662923253873], [15697835491348449269, "windows_registry", false, 9995404321852743863], [16066129441945555748, "bytes", false, 5504921018255351814], [16542808166767769916, "serde_urlencoded", false, 10457141857978290259], [16785601910559813697, "native_tls_crate", false, 14100049328723251553], [16900715236047033623, "http_body_util", false, 12374190028022742957], [18273243456331255970, "hyper_tls", false, 2021703002051677049]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-647e42a45dac08c5\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}