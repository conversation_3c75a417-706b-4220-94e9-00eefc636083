{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_sqlite\", \"any\", \"chrono\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"runtime-tokio\", \"sqlite\", \"sqlx-macros\", \"sqlx-sqlite\", \"tls-rustls\", \"tls-rustls-ring\", \"tls-rustls-ring-webpki\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlite-preupdate-hook\", \"sqlite-unbundled\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"tls-rustls-aws-lc-rs\", \"tls-rustls-ring\", \"tls-rustls-ring-native-roots\", \"tls-rustls-ring-webpki\", \"uuid\"]", "target": 3003836824758849296, "profile": 15657897354478470176, "path": 451510053532644499, "deps": [[4783846006802799581, "sqlx_sqlite", false, 1531463950699658997], [16423736323724667376, "sqlx_core", false, 12987500646962903835], [17100432655499924228, "sqlx_macros", false, 8141578760523891942]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-9f87ad529becc2b4\\dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}