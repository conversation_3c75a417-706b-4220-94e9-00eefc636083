# Ki Server Environment Configuration

# Database Configuration
DATABASE_URL=sqlite:./data/ki.db
# For production PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost:5432/ki_db

# Server Configuration
PORT=3000
RUST_LOG=info
RUST_BACKTRACE=1

# Frontend URL for CORS
FRONTEND_URL=http://localhost:5000

# Firebase Configuration
GOOGLE_APPLICATION_CREDENTIALS=./.config/gcloud/application_default_credentials.json

# SpiceDB Configuration
SPICEDB_ENDPOINT=http://localhost:50051
SPICEDB_PRESHARED_KEY=somerandomkeyhere

# SpiceDB mTLS Configuration (for production)
# Uncomment and set these for production mTLS:
# SPICEDB_ENDPOINT=https://localhost:50051
# SPICEDB_CERT_PATH=./certs/client.crt
# SPICEDB_KEY_PATH=./certs/client.key
# SPICEDB_CA_PATH=./certs/ca.crt

# Production Database Password (for docker-compose.prod.yml)
# Generate a strong password for production:
# POSTGRES_PASSWORD=your_secure_password_here
