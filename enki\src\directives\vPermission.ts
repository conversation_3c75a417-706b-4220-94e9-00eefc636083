import type { Directive, DirectiveBinding } from 'vue';
import { usePermissionsStore } from '@/stores/permissionsStore';
import { useUsersStore } from '@/stores/usersStore';

interface PermissionBinding {
    resource: string;
    resourceId: string;
    permission: string;
    userId?: string;
}

/**
 * Vue directive for conditional rendering based on permissions
 * 
 * Usage:
 * v-permission="{ resource: 'task', resourceId: 'task-123', permission: 'edit' }"
 * v-permission="{ resource: 'global_settings', resourceId: 'default', permission: 'manage_roles' }"
 */
export const vPermission: Directive<HTMLElement, PermissionBinding> = {
    async mounted(el: HTMLElement, binding: DirectiveBinding<PermissionBinding>) {
        await checkPermission(el, binding);
    },
    
    async updated(el: HTMLElement, binding: DirectiveBinding<PermissionBinding>) {
        await checkPermission(el, binding);
    }
};

async function checkPermission(el: HTMLElement, binding: DirectiveBinding<PermissionBinding>) {
    const { resource, resourceId, permission, userId } = binding.value;
    
    if (!resource || !resourceId || !permission) {
        console.warn('v-permission directive requires resource, resourceId, and permission');
        return;
    }
    
    const permissionsStore = usePermissionsStore();
    const usersStore = useUsersStore();
    
    // Get current user ID if not provided
    let currentUserId = userId;
    if (!currentUserId) {
        // Get from Firebase user and find in users store
        const firebaseUser = (window as any).firebaseUser; // This would need to be properly injected
        if (firebaseUser) {
            const user = usersStore.getUserByFirebaseId(firebaseUser.uid);
            currentUserId = user?.id;
        }
    }
    
    if (!currentUserId) {
        // Hide element if no user
        el.style.display = 'none';
        return;
    }
    
    try {
        // Check cached permission first
        const cached = permissionsStore.getCachedPermission(
            resource,
            resourceId,
            permission,
            'user',
            currentUserId
        );
        
        if (cached !== undefined) {
            el.style.display = cached.allowed ? '' : 'none';
            return;
        }
        
        // Make API call to check permission
        const hasPermission = await permissionsStore.checkSinglePermission(
            resource,
            resourceId,
            permission,
            'user',
            currentUserId
        );
        
        el.style.display = hasPermission ? '' : 'none';
    } catch (error) {
        console.error('Error checking permission:', error);
        // On error, hide the element for security
        el.style.display = 'none';
    }
}

/**
 * Helper function to check permissions programmatically
 */
export async function checkUserPermission(
    resource: string,
    resourceId: string,
    permission: string,
    userId?: string
): Promise<boolean> {
    const permissionsStore = usePermissionsStore();
    const usersStore = useUsersStore();
    
    // Get current user ID if not provided
    let currentUserId = userId;
    if (!currentUserId) {
        const firebaseUser = (window as any).firebaseUser;
        if (firebaseUser) {
            const user = usersStore.getUserByFirebaseId(firebaseUser.uid);
            currentUserId = user?.id;
        }
    }
    
    if (!currentUserId) {
        return false;
    }
    
    try {
        return await permissionsStore.checkSinglePermission(
            resource,
            resourceId,
            permission,
            'user',
            currentUserId
        );
    } catch (error) {
        console.error('Error checking permission:', error);
        return false;
    }
}

/**
 * Reactive composable for permission checking
 */
export function usePermissionCheck(
    resource: string,
    resourceId: string,
    permission: string,
    userId?: string
) {
    const permissionsStore = usePermissionsStore();
    const usersStore = useUsersStore();
    
    // Get current user ID if not provided
    let currentUserId = userId;
    if (!currentUserId) {
        const firebaseUser = (window as any).firebaseUser;
        if (firebaseUser) {
            const user = usersStore.getUserByFirebaseId(firebaseUser.uid);
            currentUserId = user?.id;
        }
    }
    
    if (!currentUserId) {
        return {
            hasPermission: false,
            loading: false,
            error: null
        };
    }
    
    // Check cached permission
    const cached = permissionsStore.getCachedPermission(
        resource,
        resourceId,
        permission,
        'user',
        currentUserId
    );
    
    return {
        hasPermission: cached?.allowed || false,
        loading: permissionsStore.loading,
        error: permissionsStore.error,
        refresh: async () => {
            return await permissionsStore.checkSinglePermission(
                resource,
                resourceId,
                permission,
                'user',
                currentUserId!
            );
        }
    };
}
