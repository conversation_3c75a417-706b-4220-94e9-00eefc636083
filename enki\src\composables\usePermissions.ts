import { computed, inject, type Ref } from 'vue';
import { usePermissionsStore } from '@/stores/permissionsStore';
import { useUsersStore } from '@/stores/usersStore';
import type { User as FirebaseUser } from 'firebase/auth';

/**
 * Composable for permission checking and management
 */
export function usePermissions() {
    const permissionsStore = usePermissionsStore();
    const usersStore = useUsersStore();
    const firebaseUser = inject<Ref<FirebaseUser | null>>('user') as Ref<FirebaseUser | null>;
    
    // Get current user ID from the users store
    const currentUserId = computed(() => {
        if (!firebaseUser.value) return null;
        const user = usersStore.getUserByFirebaseId(firebaseUser.value.uid);
        return user?.id || null;
    });
    
    /**
     * Check if current user has permission for a resource
     */
    const hasPermission = async (
        resourceType: string,
        resourceId: string,
        permission: string
    ): Promise<boolean> => {
        if (!currentUserId.value) return false;
        
        return await permissionsStore.checkSinglePermission(
            resourceType,
            resourceId,
            permission,
            'user',
            currentUserId.value
        );
    };
    
    /**
     * Check if current user can view a task
     */
    const canViewTask = async (taskId: string): Promise<boolean> => {
        return await hasPermission('task', taskId, 'view');
    };
    
    /**
     * Check if current user can edit a task
     */
    const canEditTask = async (taskId: string): Promise<boolean> => {
        return await hasPermission('task', taskId, 'edit');
    };
    
    /**
     * Check if current user can delete a task
     */
    const canDeleteTask = async (taskId: string): Promise<boolean> => {
        return await hasPermission('task', taskId, 'delete');
    };
    
    /**
     * Check if current user can manage roles
     */
    const canManageRoles = async (): Promise<boolean> => {
        return await hasPermission('global_settings', 'default', 'manage_roles');
    };
    
    /**
     * Check if current user can invite users
     */
    const canInviteUsers = async (): Promise<boolean> => {
        return await hasPermission('global_settings', 'default', 'invite_users');
    };
    
    /**
     * Add a user to a role
     */
    const addUserToRole = async (roleId: string, userId: string): Promise<boolean> => {
        return await permissionsStore.addRoleMember(roleId, userId);
    };
    
    /**
     * Remove a user from a role
     */
    const removeUserFromRole = async (roleId: string, userId: string): Promise<boolean> => {
        return await permissionsStore.removeRoleMember(roleId, userId);
    };
    
    /**
     * Set task permission for a user or role
     */
    const setTaskPermission = async (
        taskId: string,
        permission: 'owner' | 'viewer' | 'editor' | 'admin',
        subjectType: 'user' | 'role',
        subjectId: string
    ): Promise<boolean> => {
        return await permissionsStore.setTaskPermission(taskId, permission, subjectType, subjectId);
    };
    
    /**
     * Remove task permission for a user or role
     */
    const removeTaskPermission = async (
        taskId: string,
        permission: 'owner' | 'viewer' | 'editor' | 'admin',
        subjectType: 'user' | 'role',
        subjectId: string
    ): Promise<boolean> => {
        return await permissionsStore.removeTaskPermission(taskId, permission, subjectType, subjectId);
    };
    
    /**
     * Reactive computed property for checking cached permissions
     */
    const hasCachedPermission = (
        resourceType: string,
        resourceId: string,
        permission: string
    ) => computed(() => {
        if (!currentUserId.value) return false;
        
        return permissionsStore.hasPermission(
            resourceType,
            resourceId,
            permission,
            'user',
            currentUserId.value
        );
    });
    
    /**
     * Reactive computed properties for common permissions
     */
    const canManageRolesCached = computed(() => {
        if (!currentUserId.value) return false;
        return permissionsStore.hasPermission(
            'global_settings',
            'default',
            'manage_roles',
            'user',
            currentUserId.value
        );
    });
    
    const canInviteUsersCached = computed(() => {
        if (!currentUserId.value) return false;
        return permissionsStore.hasPermission(
            'global_settings',
            'default',
            'invite_users',
            'user',
            currentUserId.value
        );
    });
    
    /**
     * Clear permission cache
     */
    const clearPermissionCache = () => {
        permissionsStore.clearCache();
    };
    
    return {
        // State
        currentUserId,
        loading: computed(() => permissionsStore.loading),
        error: computed(() => permissionsStore.error),
        
        // Permission checking
        hasPermission,
        canViewTask,
        canEditTask,
        canDeleteTask,
        canManageRoles,
        canInviteUsers,
        
        // Cached permission checking
        hasCachedPermission,
        canManageRolesCached,
        canInviteUsersCached,
        
        // Role management
        addUserToRole,
        removeUserFromRole,
        
        // Task permissions
        setTaskPermission,
        removeTaskPermission,
        
        // Utilities
        clearPermissionCache,
        clearError: () => permissionsStore.clearError()
    };
}
