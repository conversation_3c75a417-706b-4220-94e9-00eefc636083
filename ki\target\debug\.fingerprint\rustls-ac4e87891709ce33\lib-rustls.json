{"rustc": 10895048813736897673, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 4443157875455849821, "deps": [[3722963349756955755, "once_cell", false, 13806597229932172998], [5491919304041016563, "ring", false, 9848939579068496548], [5986029879202738730, "log", false, 16590874038251987204], [6528079939221783635, "zeroize", false, 11906431133101562063], [8880649100394045925, "build_script_build", false, 3130630291490197103], [11782200238436109817, "<PERSON><PERSON><PERSON>", false, 215928005728270932], [16009134724182327169, "pki_types", false, 2397219111230403854], [17003143334332120809, "subtle", false, 8346554607052063111]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-ac4e87891709ce33\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}