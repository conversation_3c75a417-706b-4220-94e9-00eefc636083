{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 2241668132362809309, "path": 8747718756401981818, "deps": [[40386456601120721, "percent_encoding", false, 15857642291582845488], [784494742817713399, "tower_service", false, 5023113295579284643], [1906322745568073236, "pin_project_lite", false, 13483621581492765273], [1999399154011168049, "rustversion", false, 12095854159049431513], [2517136641825875337, "sync_wrapper", false, 17561740311626693908], [3129130049864710036, "memchr", false, 16328662395844467194], [4359148418957042248, "axum_core", false, 14835735922130185531], [5695049318159433696, "tower", false, 4140339503730607532], [7695812897323945497, "itoa", false, 12280231955184405792], [7712452662827335977, "tower_layer", false, 14302084456410726127], [9010263965687315507, "http", false, 13780035913158987009], [9678799920983747518, "matchit", false, 5235669237314307135], [9689903380558560274, "serde", false, 13671997056944884096], [10229185211513642314, "mime", false, 15775420741624474642], [10629569228670356391, "futures_util", false, 12506543492908000869], [11946729385090170470, "async_trait", false, 10765758345576346120], [14084095096285906100, "http_body", false, 13164054679762100366], [16066129441945555748, "bytes", false, 14214947457689394275], [16900715236047033623, "http_body_util", false, 11044969495969053557]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-4f4723e231f9c75c\\dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}