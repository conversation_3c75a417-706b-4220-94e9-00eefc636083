<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, User, Search } from '@element-plus/icons-vue';
import { useRolesStore } from '@/stores/rolesStore';
import { useUsersStore } from '@/stores/usersStore';
import { usePermissions } from '@/composables/usePermissions';
import type { Role, RoleMember } from '@/types';

const props = defineProps<{
    modelValue: boolean;
    role?: Role | null;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: boolean];
    'close': [];
}>();

const rolesStore = useRolesStore();
const usersStore = useUsersStore();
const { addUserToRole, removeUserFromRole } = usePermissions();

// State
const loading = ref(false);
const searchQuery = ref('');
const showAddMemberDialog = ref(false);
const selectedUsers = ref<string[]>([]);

// Computed properties
const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});

const roleDetails = computed(() => {
    return props.role ? rolesStore.getRoleDetails(props.role.id) : null;
});

const availableUsers = computed(() => {
    if (!roleDetails.value) return [];
    
    const memberIds = new Set(roleDetails.value.members.map(m => m.user_id));
    return usersStore.users.filter(user => !memberIds.has(user.id));
});

const filteredAvailableUsers = computed(() => {
    if (!searchQuery.value) return availableUsers.value;
    
    const query = searchQuery.value.toLowerCase();
    return availableUsers.value.filter(user => 
        user.display_name?.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query)
    );
});

// Watch for role changes
watch(() => props.role, async (newRole) => {
    if (newRole && props.modelValue) {
        await loadRoleDetails();
    }
}, { immediate: true });

// Methods
const loadRoleDetails = async () => {
    if (!props.role) return;
    
    loading.value = true;
    try {
        await rolesStore.loadRoleDetails(props.role.id);
    } catch (error) {
        console.error('Failed to load role details:', error);
        ElMessage.error('Failed to load role members');
    } finally {
        loading.value = false;
    }
};

const handleClose = () => {
    dialogVisible.value = false;
    emit('close');
};

const openAddMemberDialog = () => {
    selectedUsers.value = [];
    searchQuery.value = '';
    showAddMemberDialog.value = true;
};

const handleAddMembers = async () => {
    if (!props.role || selectedUsers.value.length === 0) return;
    
    loading.value = true;
    try {
        const promises = selectedUsers.value.map(userId => 
            addUserToRole(props.role!.id, userId)
        );
        
        const results = await Promise.all(promises);
        const successCount = results.filter(Boolean).length;
        
        if (successCount > 0) {
            ElMessage.success(`Added ${successCount} member(s) successfully`);
            await loadRoleDetails();
        }
        
        if (successCount < selectedUsers.value.length) {
            ElMessage.warning('Some members could not be added');
        }
        
        showAddMemberDialog.value = false;
        selectedUsers.value = [];
    } catch (error) {
        console.error('Failed to add members:', error);
        ElMessage.error('Failed to add members');
    } finally {
        loading.value = false;
    }
};

const removeMember = async (member: RoleMember) => {
    if (!props.role) return;
    
    try {
        await ElMessageBox.confirm(
            `Remove ${member.display_name || member.email} from ${props.role.name}?`,
            'Confirm Remove',
            {
                confirmButtonText: 'Remove',
                cancelButtonText: 'Cancel',
                type: 'warning',
                confirmButtonClass: 'el-button--danger'
            }
        );
        
        loading.value = true;
        const success = await removeUserFromRole(props.role.id, member.user_id);
        
        if (success) {
            ElMessage.success('Member removed successfully');
            await loadRoleDetails();
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('Failed to remove member:', error);
            ElMessage.error('Failed to remove member');
        }
    } finally {
        loading.value = false;
    }
};

const getUserAvatar = (member: RoleMember) => {
    return member.photo_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(member.display_name || member.email)}&background=random`;
};
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        :title="`Manage Members - ${role?.name}`"
        width="600px"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <div v-if="loading && !roleDetails" class="loading-container">
            <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else-if="roleDetails" class="members-content">
            <!-- Header -->
            <div class="members-header">
                <div class="header-info">
                    <h3>Members ({{ roleDetails.member_count }})</h3>
                    <p class="header-description">
                        Manage who has access to this role and its permissions
                    </p>
                </div>
                <el-button 
                    type="primary" 
                    :icon="Plus" 
                    @click="openAddMemberDialog"
                    :disabled="availableUsers.length === 0"
                >
                    Add Members
                </el-button>
            </div>
            
            <!-- Members List -->
            <div class="members-list">
                <div v-if="roleDetails.members.length === 0" class="empty-state">
                    <p>No members in this role yet.</p>
                    <el-button type="primary" @click="openAddMemberDialog">
                        Add First Member
                    </el-button>
                </div>
                
                <div v-else class="member-items">
                    <div 
                        v-for="member in roleDetails.members" 
                        :key="member.user_id"
                        class="member-item"
                    >
                        <div class="member-info">
                            <el-avatar 
                                :src="getUserAvatar(member)"
                                :size="40"
                            >
                                <el-icon><User /></el-icon>
                            </el-avatar>
                            <div class="member-details">
                                <div class="member-name">
                                    {{ member.display_name || 'Unknown User' }}
                                </div>
                                <div class="member-email">
                                    {{ member.email }}
                                </div>
                            </div>
                        </div>
                        <el-button 
                            :icon="Delete" 
                            type="danger" 
                            size="small"
                            @click="removeMember(member)"
                            :loading="loading"
                        >
                            Remove
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
        
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">Close</el-button>
            </div>
        </template>
    </el-dialog>
    
    <!-- Add Members Dialog -->
    <el-dialog
        v-model="showAddMemberDialog"
        title="Add Members"
        width="500px"
        :close-on-click-modal="false"
    >
        <div class="add-members-content">
            <!-- Search -->
            <el-input
                v-model="searchQuery"
                placeholder="Search users by name or email"
                :prefix-icon="Search"
                clearable
                class="search-input"
            />
            
            <!-- User Selection -->
            <div class="user-selection">
                <div v-if="filteredAvailableUsers.length === 0" class="empty-state">
                    <p v-if="availableUsers.length === 0">
                        All users are already members of this role.
                    </p>
                    <p v-else>
                        No users found matching your search.
                    </p>
                </div>
                
                <el-checkbox-group v-else v-model="selectedUsers" class="user-list">
                    <div 
                        v-for="user in filteredAvailableUsers" 
                        :key="user.id"
                        class="user-item"
                    >
                        <el-checkbox :label="user.id" class="user-checkbox">
                            <div class="user-info">
                                <el-avatar 
                                    :src="user.photo_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.display_name || user.email)}&background=random`"
                                    :size="32"
                                >
                                    <el-icon><User /></el-icon>
                                </el-avatar>
                                <div class="user-details">
                                    <div class="user-name">
                                        {{ user.display_name || 'Unknown User' }}
                                    </div>
                                    <div class="user-email">
                                        {{ user.email }}
                                    </div>
                                </div>
                            </div>
                        </el-checkbox>
                    </div>
                </el-checkbox-group>
            </div>
        </div>
        
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="showAddMemberDialog = false">Cancel</el-button>
                <el-button 
                    type="primary" 
                    @click="handleAddMembers"
                    :disabled="selectedUsers.length === 0"
                    :loading="loading"
                >
                    Add {{ selectedUsers.length }} Member(s)
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.loading-container {
    padding: 24px;
}

.members-content {
    max-height: 500px;
    overflow-y: auto;
}

.members-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--el-border-color-light);
}

.header-info h3 {
    margin: 0 0 4px 0;
    color: var(--el-text-color-primary);
}

.header-description {
    margin: 0;
    font-size: 14px;
    color: var(--el-text-color-regular);
}

.members-list {
    min-height: 200px;
}

.empty-state {
    text-align: center;
    padding: 48px 16px;
    color: var(--el-text-color-regular);
}

.member-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.member-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    background: var(--el-bg-color);
}

.member-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.member-details {
    flex: 1;
}

.member-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 2px;
}

.member-email {
    font-size: 12px;
    color: var(--el-text-color-regular);
}

.add-members-content {
    max-height: 400px;
    overflow-y: auto;
}

.search-input {
    margin-bottom: 16px;
}

.user-selection {
    min-height: 200px;
}

.user-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.user-item {
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    padding: 8px;
    background: var(--el-bg-color);
}

.user-checkbox {
    width: 100%;
    margin: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 8px;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 2px;
}

.user-email {
    font-size: 12px;
    color: var(--el-text-color-regular);
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
