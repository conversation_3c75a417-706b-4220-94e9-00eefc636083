{"rustc": 10895048813736897673, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 9025750215440372010, "profile": 15657897354478470176, "path": 4067448470675943552, "deps": [[2739579679802620019, "prost_build", false, 8374450587349346887], [8549471757621926118, "prettyplease", false, 5216991168601021802], [8986759836770526006, "syn", false, 11755841403949231707], [12410540580958238005, "proc_macro2", false, 3329383689787929075], [16470553738848018267, "prost_types", false, 14990870458343803241], [17990358020177143287, "quote", false, 17848197058811471874]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tonic-build-1f2f42f2bce3b145\\dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}