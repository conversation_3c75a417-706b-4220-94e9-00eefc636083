{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_sqlite\", \"_tls-rustls-ring-webpki\", \"chrono\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2175425913391121376, "path": 11495661155069631410, "deps": [[530211389790465181, "hex", false, 14601131202669845500], [3150220818285335163, "url", false, 13713138443184090826], [3405707034081185165, "dotenvy", false, 14968311870511842935], [3722963349756955755, "once_cell", false, 13806597229932172998], [4783846006802799581, "sqlx_sqlite", false, 6524937960169225432], [5138218615291878843, "tokio", false, 9931954201191909963], [5236433071915784494, "sha2", false, 12029549537769801891], [8986759836770526006, "syn", false, 10509934587567121032], [9689903380558560274, "serde", false, 5420700675791837695], [12170264697963848012, "either", false, 16889443152687096583], [12261610614527126074, "tempfile", false, 990995257040070165], [12410540580958238005, "proc_macro2", false, 15246686890710488487], [13077543566650298139, "heck", false, 11301959593663298675], [15367738274754116744, "serde_json", false, 16065851662923253873], [16423736323724667376, "sqlx_core", false, 6512255650626670360], [17990358020177143287, "quote", false, 10330838453297999663]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-36fa8ac8a4e16266\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}