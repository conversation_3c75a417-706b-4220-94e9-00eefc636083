{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"_tls-rustls-ring-webpki\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-native-certs\", \"serde\", \"serde_json\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 5187057522005616952, "path": 13515265155780315145, "deps": [[5103565458935487, "futures_io", false, 13329666739313697901], [40386456601120721, "percent_encoding", false, 7865310525752909446], [788558663644978524, "crossbeam_queue", false, 2462965858714205341], [1303438375223863970, "hashlink", false, 144757091014228209], [3129130049864710036, "memchr", false, 297644545778223844], [3150220818285335163, "url", false, 6767044490433710867], [3646857438214563691, "futures_intrusive", false, 4484575495402623007], [3722963349756955755, "once_cell", false, 13806597229932172998], [5138218615291878843, "tokio", false, 1907159425947619226], [5236433071915784494, "sha2", false, 7960054426711535580], [5304461447358541099, "crc", false, 4423808948881408939], [5986029879202738730, "log", false, 16590874038251987204], [6048213226671835012, "smallvec", false, 2873255965622490284], [6547980334806251551, "chrono", false, 12524342269680542231], [7620660491849607393, "futures_core", false, 12714968455384343301], [8269115081296425610, "uuid", false, 796264558990639248], [8606274917505247608, "tracing", false, 11810089682127676548], [8880649100394045925, "rustls", false, 7242912850233273249], [9061476533697426406, "event_listener", false, 1959808672641848372], [9689903380558560274, "serde", false, 7510950832928885797], [10629569228670356391, "futures_util", false, 18269551313004061405], [10806645703491011684, "thiserror", false, 2323351879934818550], [12170264697963848012, "either", false, 7616439343094761338], [13077212702700853852, "base64", false, 16688224880384710644], [14483812548788871374, "indexmap", false, 6654181108458255245], [15367738274754116744, "serde_json", false, 11829850397741493408], [15594963368141592132, "webpki_roots", false, 1496401315291307744], [16066129441945555748, "bytes", false, 5504921018255351814], [16973251432615581304, "tokio_stream", false, 15272473048171049746], [17286916485753290591, "hashbrown", false, 16385370271478724278]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-ad77801aac80a1e2\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}