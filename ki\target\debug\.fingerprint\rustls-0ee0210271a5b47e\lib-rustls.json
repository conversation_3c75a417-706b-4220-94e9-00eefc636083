{"rustc": 10895048813736897673, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 16865373277404624699, "path": 4443157875455849821, "deps": [[3722963349756955755, "once_cell", false, 15354684742482638160], [5491919304041016563, "ring", false, 16752502457284854848], [6528079939221783635, "zeroize", false, 6887383267434897057], [8880649100394045925, "build_script_build", false, 38744174574752901], [11782200238436109817, "<PERSON><PERSON><PERSON>", false, 13403108619883119620], [16009134724182327169, "pki_types", false, 10025315460724084507], [17003143334332120809, "subtle", false, 15237298131658588644]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-0ee0210271a5b47e\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}