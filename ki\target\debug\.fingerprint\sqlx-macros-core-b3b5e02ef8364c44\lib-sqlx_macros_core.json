{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_sqlite\", \"_tls-rustls-ring-webpki\", \"chrono\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2175425913391121376, "path": 11495661155069631410, "deps": [[530211389790465181, "hex", false, 14601131202669845500], [3150220818285335163, "url", false, 17247892221037556890], [3405707034081185165, "dotenvy", false, 14968311870511842935], [3722963349756955755, "once_cell", false, 13806597229932172998], [4783846006802799581, "sqlx_sqlite", false, 13936596913522110722], [5138218615291878843, "tokio", false, 9931954201191909963], [5236433071915784494, "sha2", false, 12029549537769801891], [8986759836770526006, "syn", false, 2690035167543797025], [9689903380558560274, "serde", false, 7510950832928885797], [12170264697963848012, "either", false, 1763564642371084670], [12261610614527126074, "tempfile", false, 11021454074809072445], [12410540580958238005, "proc_macro2", false, 3329383689787929075], [13077543566650298139, "heck", false, 13635324434202194828], [15367738274754116744, "serde_json", false, 11829850397741493408], [16423736323724667376, "sqlx_core", false, 10649370956146555356], [17990358020177143287, "quote", false, 17848197058811471874]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-b3b5e02ef8364c44\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}