version: '3.8'

services:
  ki:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
      - ./.config:/app/.config
      - ./certs:/app/certs:ro  # Mount certificates as read-only
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=./app/.config/gcloud/application_default_credentials.json
      - DATABASE_URL=sqlite:/app/data/ki.db
      - PORT=3000
      - RUST_LOG=info
      - RUST_BACKTRACE=1
      # SpiceDB with mTLS
      - SPICEDB_ENDPOINT=https://spicedb:50051
      - SPICEDB_PRESHARED_KEY=${SPICEDB_PRESHARED_KEY}
      - SPICEDB_CERT_PATH=/app/certs/client.crt
      - SPICEDB_KEY_PATH=/app/certs/client.key
      - SPICEDB_CA_PATH=/app/certs/ca.crt
    depends_on:
      - spicedb
    networks:
      - ki-network

  spicedb:
    image: "quay.io/authzed/spicedb:v1.30.0"
    command: "serve"
    restart: "always"
    ports:
      - "8080:8080"   # HTTP API
      - "9090:9090"   # Metrics
      - "50051:50051" # gRPC API
    volumes:
      - ./certs:/etc/spicedb/certs:ro  # Mount certificates
      - spicedb-data:/var/lib/spicedb  # Persistent data
    environment:
      # Production datastore (PostgreSQL)
      - "SPICEDB_DATASTORE_ENGINE=postgres"
      - "SPICEDB_DATASTORE_CONN_URI=postgres://spicedb:${POSTGRES_PASSWORD}@postgres:5432/spicedb?sslmode=require"
      
      # mTLS Configuration
      - "SPICEDB_GRPC_TLS_CERT_PATH=/etc/spicedb/certs/server.crt"
      - "SPICEDB_GRPC_TLS_KEY_PATH=/etc/spicedb/certs/server.key"
      - "SPICEDB_GRPC_TLS_CLIENT_CA_PATH=/etc/spicedb/certs/ca.crt"
      - "SPICEDB_GRPC_TLS_CLIENT_AUTH_TYPE=RequireAndVerifyClientCert"
      
      # Authentication
      - "SPICEDB_GRPC_PRESHARED_KEY=${SPICEDB_PRESHARED_KEY}"
      
      # Logging and monitoring
      - "SPICEDB_LOG_LEVEL=info"
      - "SPICEDB_METRICS_ENABLED=true"
      
      # Performance tuning
      - "SPICEDB_DATASTORE_MAX_OPEN_CONNS=20"
      - "SPICEDB_DATASTORE_MAX_IDLE_CONNS=10"
      - "SPICEDB_DATASTORE_CONN_MAX_LIFETIME=30m"
    depends_on:
      - postgres
    networks:
      - ki-network

  postgres:
    image: postgres:15-alpine
    restart: always
    environment:
      - POSTGRES_DB=spicedb
      - POSTGRES_USER=spicedb
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - ki-network

volumes:
  spicedb-data:
  postgres-data:

networks:
  ki-network:
    driver: bridge
