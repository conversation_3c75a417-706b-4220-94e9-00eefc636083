{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 15657897354478470176, "path": 10925507480384047280, "deps": [[784494742817713399, "tower_service", false, 4681011003402000622], [1906322745568073236, "pin_project_lite", false, 8745154965143889200], [1999399154011168049, "rustversion", false, 12095854159049431513], [2517136641825875337, "sync_wrapper", false, 7530985440802629266], [7712452662827335977, "tower_layer", false, 4286392900353169829], [9010263965687315507, "http", false, 8244477843949077449], [10229185211513642314, "mime", false, 7251293154870877873], [10629569228670356391, "futures_util", false, 18269551313004061405], [11946729385090170470, "async_trait", false, 10765758345576346120], [14084095096285906100, "http_body", false, 2259226771228292901], [16066129441945555748, "bytes", false, 5504921018255351814], [16900715236047033623, "http_body_util", false, 12374190028022742957]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-26991af45332f8c9\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}